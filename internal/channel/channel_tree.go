package channel

import (
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/uozi-tech/cosy"
)

type Tree Node

// GetAllDescendantIDs 递归获取当前节点及其所有子节点的用户ID列表
func (t *Tree) GetAllDescendantIDs() []uint64 {
	// 将Tree转为Node，使用Node的方法
	return (*Node)(t).GetAllDescendantIDs()
}

// GetChannelTree 获取完整的渠道树结构
// userId: 用户ID，作为查询的起始点
// 返回该用户所在树的完整结构
func GetChannelTree(userId uint64) (c *Tree, err error) {
	return getChannelTreeWithMode(userId, false)
}

// GetChannelChildren 只获取当前节点的直接子节点
// userId: 用户ID，作为查询的父节点
// 返回该用户的所有直接下级节点
func GetChannelChildren(userId uint64) (c *Tree, err error) {
	return getChannelTreeWithMode(userId, true)
}

// getChannelTreeWithMode 根据不同模式获取渠道树
// userId: 用户ID
// onlyChildren: 是否只获取直接子节点
// - true: 只返回当前节点及其直接子节点
// - false: 返回完整树结构
func getChannelTreeWithMode(userId uint64, onlyChildren bool) (c *Tree, err error) {
	q := query.Channel

	var chs []*model.Channel
	var user *model.User

	if onlyChildren {
		// 仅子节点模式：只查询当前用户ID作为父节点的记录
		chs, _ = q.Where(q.UserID.Eq(userId)).
			Preload(q.RootUser, q.User, q.RelateUser).
			Find()

		// 如果没有找到记录，尝试获取用户信息
		if len(chs) == 0 {
			u := query.User
			user, err = u.FirstByID(userId)
			if err != nil {
				return nil, err
			}
		}
	} else {
		// 完整树模式：查询整个树
		chs, _ = q.Where(q.RootUserID.Eq(userId)).
			Preload(q.RootUser, q.User, q.RelateUser).
			Find()

		// 如果没有找到记录，创建新的渠道记录
		if len(chs) == 0 {
			u := query.User

			user, err = u.FirstByID(userId)
			if err != nil {
				return nil, err
			}

			// 尝试查找当前用户是否有上级节点
			chnParent, _ := q.Where(q.RelateUserID.Eq(userId)).Preload(q.RootUser).First()

			rootID := userId

			var rootUser *model.User
			if chnParent != nil {
				rootID = chnParent.RootUserID
				rootUser = chnParent.RootUser
			}

			// 创建新的渠道记录
			chn := &model.Channel{
				RootUserID: rootID,
				RootUser:   rootUser,
				UserID:     userId,
				User:       user,
			}

			db := cosy.UseDB()

			// 存在则更新，不存在则创建
			db.Where("user_id", userId).FirstOrCreate(chn)

			// 重新查询整个树
			chs, _ = q.Where(q.RootUserID.Eq(chn.RootUserID)).
				Preload(q.RootUser, q.User, q.RelateUser).
				Find()

			userId = chn.RootUserID
		}
	}

	// 用于构建树的节点映射
	channelMap := make(map[uint64]*Node)

	if onlyChildren {
		// 仅子节点模式：构建当前节点作为根节点
		if user != nil || (len(chs) > 0 && chs[0].User != nil) {
			currentUser := user
			if currentUser == nil && len(chs) > 0 {
				currentUser = chs[0].User
			}

			// 创建当前节点
			channelMap[userId] = &Node{
				Name:      currentUser.Name,
				RootID:    userId, // 在仅子节点模式下，当前节点就是根节点
				CurrentID: userId,
				Children:  make([]*Node, 0),
			}
		}

		// 构建所有直接子节点
		for _, v := range chs {
			if v.RelateUser != nil {
				// 创建子节点（如果不存在）
				if channelMap[v.RelateUserID] == nil {
					t := &Node{
						Name:      v.RelateUser.Name,
						CurrentID: v.RelateUserID,
						RootID:    userId, // 在仅子节点模式下，设置当前查询的userId为根
						Children:  make([]*Node, 0),
					}
					channelMap[v.RelateUserID] = t
				}

				// 将子节点添加到当前节点的子节点列表中
				if channelMap[userId] != nil {
					channelMap[userId].Children = append(channelMap[userId].Children, channelMap[v.RelateUserID])
				}
			}
		}
	} else {
		// 完整树模式：先创建所有节点
		for _, v := range chs {
			if channelMap[v.UserID] == nil {
				channelMap[v.UserID] = &Node{
					Name:      v.User.Name,
					RootID:    v.RootUserID,
					CurrentID: v.UserID,
					Children:  make([]*Node, 0),
				}
			}
		}

		// 然后构建节点之间的父子关系
		for _, v := range chs {
			if v.RelateUser != nil {
				// 创建关联用户节点（如果不存在）
				if channelMap[v.RelateUserID] == nil {
					t := &Node{
						Name:      v.RelateUser.Name,
						CurrentID: v.RelateUserID,
						RootID:    v.RootUserID,
						Children:  make([]*Node, 0),
					}
					channelMap[v.RelateUserID] = t
				}

				// 构建父子关系：将关联用户节点添加为当前节点的子节点
				channelMap[v.UserID].Children = append(channelMap[v.UserID].Children, channelMap[v.RelateUserID])
			}
		}
	}

	return (*Tree)(channelMap[userId]), nil
}
